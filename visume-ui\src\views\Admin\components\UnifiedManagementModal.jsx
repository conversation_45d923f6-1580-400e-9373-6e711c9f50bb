import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import PropTypes from "prop-types";

/**
 * Unified Management Modal for handling both candidate Visume limits and employer credits
 */
const UnifiedManagementModal = ({
  isOpen,
  onClose,
  onSave,
  selectedEntity,
  entityType, // "candidate" or "employer"
  value,
  setValue,
  isUpdating,
}) => {
  // Determine modal configuration based on entity type
  const config = {
    candidate: {
      title: `Manage Visume Limit for ${selectedEntity?.cand_name || selectedEntity?.name || ""}`,
      fieldLabel: "Visume Limit",
      fieldPlaceholder: "Enter visume limit (minimum 1)",
      helpText: [
        "• Minimum visume limit: 1 Visume (usage history will be reset)",
        "• Set the maximum number of Visumes this candidate can create",
        "• Usage counter will be reset to zero when limit is updated",
      ],
      quickOptions: [
        { value: "1", label: "1 Visume", color: "blue" },
        { value: "3", label: "3 Visumes", color: "green" },
        { value: "5", label: "5 Visumes", color: "purple" },
        { value: "10", label: "10 Visumes", color: "orange" },
      ],
      saveButtonText: isUpdating ? "Updating..." : "Save Changes",
      minValue: 1,
      validationMessage: "Visume limit must be at least 1 Visume",
    },
    employer: {
      title: `Edit Credits for ${selectedEntity?.name || ""}`,
      fieldLabel: "Credits Amount",
      fieldPlaceholder: "Enter credits amount",
      helpText: [
        "• Credits are used to unlock candidate profiles",
        "• Minimum credits: 0 (account will be marked as expired)",
        "• Credits can be increased at any time",
      ],
      quickOptions: [
        { value: "15", label: "15 Credits", color: "blue" },
        { value: "20", label: "20 Credits", color: "green" },
        { value: "25", label: "25 Credits", color: "purple" },
        { value: "50", label: "50 Credits", color: "orange" },
      ],
      saveButtonText: isUpdating ? "Updating..." : "Save Changes",
      minValue: 0,
      validationMessage: "Credits cannot be negative",
    },
  };

  // Animation state management
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure the DOM is updated before starting the animation
      setTimeout(() => setIsVisible(true), 10);
      // Lock body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      setIsVisible(false);
      // Wait for the exit animation to complete before unmounting
      const timer = setTimeout(() => {
        setShouldRender(false);
        // Restore body scroll when modal is closed
        document.body.style.overflow = 'unset';
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Get the appropriate configuration
  const currentConfig = config[entityType];

  if (!shouldRender || !selectedEntity) return null;

  const modalContent = (
    <div 
      className={`fixed inset-0 flex items-center justify-center transition-all duration-300 ${
        isVisible ? 'bg-black bg-opacity-50 backdrop-blur-sm' : 'bg-black bg-opacity-0 backdrop-blur-none'
      }`}
      style={{
        zIndex: 9999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        // Fallback for browsers that don't support backdrop-filter
        WebkitBackdropFilter: isVisible ? 'blur(4px)' : 'none',
        backdropFilter: isVisible ? 'blur(4px)' : 'none'
      }}
      onClick={(e) => {
        // Close modal when clicking on the backdrop (not the modal content)
        if (e.target === e.currentTarget && !isUpdating) {
          onClose();
        }
      }}
    >
      <div 
        className={`bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100 ring-1 ring-gray-200 dark:ring-gray-700' : 'scale-95 opacity-0'
        }`}
        style={{
          boxShadow: isVisible ? '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : 'none'
        }}
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {currentConfig.title}
        </h3>

        {/* Quick Options */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Quick {entityType === "candidate" ? "Visume" : "Credit"} Options
          </label>
          <div className="grid grid-cols-4 gap-3 mb-4">
            {currentConfig.quickOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => setValue(option.value)}
                className={`px-4 py-3 bg-gradient-to-r from-${option.color}-500 to-${option.color}-600 hover:from-${option.color}-600 hover:to-${option.color}-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg`}
                disabled={isUpdating}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Custom Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {currentConfig.fieldLabel}
          </label>
          <input
            type="number"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            min={currentConfig.minValue}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            placeholder={currentConfig.fieldPlaceholder}
            disabled={isUpdating}
          />
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {currentConfig.helpText.map((text, index) => (
              <React.Fragment key={index}>
                {text}
                <br />
              </React.Fragment>
            ))}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onSave}
            disabled={isUpdating}
            className="flex-1 bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            {currentConfig.saveButtonText}
          </button>
          <button
            onClick={() => !isUpdating && onClose()}
            disabled={isUpdating}
            className="flex-1 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

UnifiedManagementModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  selectedEntity: PropTypes.object,
  entityType: PropTypes.oneOf(["candidate", "employer"]).isRequired,
  value: PropTypes.string.isRequired,
  setValue: PropTypes.func.isRequired,
  isUpdating: PropTypes.bool,
};

UnifiedManagementModal.defaultProps = {
  isUpdating: false,
};

export default UnifiedManagementModal;