import {
  require_jsx_runtime
} from "./chunk-DZUOJV22.js";
import {
  require_react_dom
} from "./chunk-V5LT2MCF.js";
import {
  require_react
} from "./chunk-GHX6QOSA.js";
import {
  __commonJS,
  __toESM
} from "./chunk-2GTGKKMZ.js";

// node_modules/classnames/index.js
var require_classnames = __commonJS({
  "node_modules/classnames/index.js"(exports, module) {
    (function() {
      "use strict";
      var hasOwn = {}.hasOwnProperty;
      var nativeCodeString = "[native code]";
      function classNames() {
        var classes2 = [];
        for (var i = 0; i < arguments.length; i++) {
          var arg = arguments[i];
          if (!arg)
            continue;
          var argType = typeof arg;
          if (argType === "string" || argType === "number") {
            classes2.push(arg);
          } else if (Array.isArray(arg)) {
            if (arg.length) {
              var inner = classNames.apply(null, arg);
              if (inner) {
                classes2.push(inner);
              }
            }
          } else if (argType === "object") {
            if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes("[native code]")) {
              classes2.push(arg.toString());
              continue;
            }
            for (var key in arg) {
              if (hasOwn.call(arg, key) && arg[key]) {
                classes2.push(key);
              }
            }
          }
        }
        return classes2.join(" ");
      }
      if (typeof module !== "undefined" && module.exports) {
        classNames.default = classNames;
        module.exports = classNames;
      } else if (typeof define === "function" && typeof define.amd === "object" && define.amd) {
        define("classnames", [], function() {
          return classNames;
        });
      } else {
        window.classNames = classNames;
      }
    })();
  }
});

// node_modules/react-responsive-modal/dist/index.js
var import_react3 = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var import_classnames = __toESM(require_classnames());

// node_modules/@bedrock-layout/use-stateful-ref/lib/index.m.js
var import_react = __toESM(require_react());
function s(u) {
  let [e, f] = import_react.default.useState(u);
  const { current: r } = import_react.default.useRef({
    current: e
  });
  return Object.defineProperty(r, "current", {
    get: () => e,
    // eslint-disable-next-line functional/no-return-void
    set: (t) => {
      Object.is(e, t) || (e = t, f(t));
    }
  }), r;
}

// node_modules/@bedrock-layout/use-forwarded-ref/lib/index.m.js
var import_react2 = __toESM(require_react());
function c2(t, r = { isStateful: true }) {
  const f = s(), u = (0, import_react2.useRef)(), e = r.isStateful ? f : u;
  return import_react2.default.useImperativeHandle(t, () => e.current), e;
}

// node_modules/react-responsive-modal/dist/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime());

// node_modules/body-scroll-lock/lib/bodyScrollLock.esm.js
function _toConsumableArray(arr) {
  if (Array.isArray(arr)) {
    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
      arr2[i] = arr[i];
    }
    return arr2;
  } else {
    return Array.from(arr);
  }
}
var hasPassiveEvents = false;
if (typeof window !== "undefined") {
  passiveTestOptions = {
    get passive() {
      hasPassiveEvents = true;
      return void 0;
    }
  };
  window.addEventListener("testPassive", null, passiveTestOptions);
  window.removeEventListener("testPassive", null, passiveTestOptions);
}
var passiveTestOptions;
var isIosDevice = typeof window !== "undefined" && window.navigator && window.navigator.platform && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === "MacIntel" && window.navigator.maxTouchPoints > 1);
var locks = [];
var documentListenerAdded = false;
var initialClientY = -1;
var previousBodyOverflowSetting = void 0;
var previousBodyPaddingRight = void 0;
var allowTouchMove = function allowTouchMove2(el) {
  return locks.some(function(lock) {
    if (lock.options.allowTouchMove && lock.options.allowTouchMove(el)) {
      return true;
    }
    return false;
  });
};
var preventDefault = function preventDefault2(rawEvent) {
  var e = rawEvent || window.event;
  if (allowTouchMove(e.target)) {
    return true;
  }
  if (e.touches.length > 1)
    return true;
  if (e.preventDefault)
    e.preventDefault();
  return false;
};
var setOverflowHidden = function setOverflowHidden2(options) {
  if (previousBodyPaddingRight === void 0) {
    var _reserveScrollBarGap = !!options && options.reserveScrollBarGap === true;
    var scrollBarGap = window.innerWidth - document.documentElement.clientWidth;
    if (_reserveScrollBarGap && scrollBarGap > 0) {
      previousBodyPaddingRight = document.body.style.paddingRight;
      document.body.style.paddingRight = scrollBarGap + "px";
    }
  }
  if (previousBodyOverflowSetting === void 0) {
    previousBodyOverflowSetting = document.body.style.overflow;
    document.body.style.overflow = "hidden";
  }
};
var restoreOverflowSetting = function restoreOverflowSetting2() {
  if (previousBodyPaddingRight !== void 0) {
    document.body.style.paddingRight = previousBodyPaddingRight;
    previousBodyPaddingRight = void 0;
  }
  if (previousBodyOverflowSetting !== void 0) {
    document.body.style.overflow = previousBodyOverflowSetting;
    previousBodyOverflowSetting = void 0;
  }
};
var isTargetElementTotallyScrolled = function isTargetElementTotallyScrolled2(targetElement) {
  return targetElement ? targetElement.scrollHeight - targetElement.scrollTop <= targetElement.clientHeight : false;
};
var handleScroll = function handleScroll2(event, targetElement) {
  var clientY = event.targetTouches[0].clientY - initialClientY;
  if (allowTouchMove(event.target)) {
    return false;
  }
  if (targetElement && targetElement.scrollTop === 0 && clientY > 0) {
    return preventDefault(event);
  }
  if (isTargetElementTotallyScrolled(targetElement) && clientY < 0) {
    return preventDefault(event);
  }
  event.stopPropagation();
  return true;
};
var disableBodyScroll = function disableBodyScroll2(targetElement, options) {
  if (!targetElement) {
    console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");
    return;
  }
  if (locks.some(function(lock2) {
    return lock2.targetElement === targetElement;
  })) {
    return;
  }
  var lock = {
    targetElement,
    options: options || {}
  };
  locks = [].concat(_toConsumableArray(locks), [lock]);
  if (isIosDevice) {
    targetElement.ontouchstart = function(event) {
      if (event.targetTouches.length === 1) {
        initialClientY = event.targetTouches[0].clientY;
      }
    };
    targetElement.ontouchmove = function(event) {
      if (event.targetTouches.length === 1) {
        handleScroll(event, targetElement);
      }
    };
    if (!documentListenerAdded) {
      document.addEventListener("touchmove", preventDefault, hasPassiveEvents ? { passive: false } : void 0);
      documentListenerAdded = true;
    }
  } else {
    setOverflowHidden(options);
  }
};
var enableBodyScroll = function enableBodyScroll2(targetElement) {
  if (!targetElement) {
    console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");
    return;
  }
  locks = locks.filter(function(lock) {
    return lock.targetElement !== targetElement;
  });
  if (isIosDevice) {
    targetElement.ontouchstart = null;
    targetElement.ontouchmove = null;
    if (documentListenerAdded && locks.length === 0) {
      document.removeEventListener("touchmove", preventDefault, hasPassiveEvents ? { passive: false } : void 0);
      documentListenerAdded = false;
    }
  } else if (!locks.length) {
    restoreOverflowSetting();
  }
};

// node_modules/react-responsive-modal/dist/index.js
var CloseIcon = ({ classes: classes$1, classNames, styles, id, closeIcon, onClick }) => (0, import_jsx_runtime.jsx)("button", {
  id,
  className: (0, import_classnames.default)(classes$1.closeButton, classNames == null ? void 0 : classNames.closeButton),
  style: styles == null ? void 0 : styles.closeButton,
  onClick,
  "data-testid": "close-button",
  children: closeIcon ? closeIcon : (0, import_jsx_runtime.jsx)("svg", {
    className: classNames == null ? void 0 : classNames.closeIcon,
    style: styles == null ? void 0 : styles.closeIcon,
    width: 28,
    height: 28,
    viewBox: "0 0 36 36",
    "data-testid": "close-icon",
    children: (0, import_jsx_runtime.jsx)("path", { d: "M28.5 9.62L26.38 7.5 18 15.88 9.62 7.5 7.5 9.62 15.88 18 7.5 26.38l2.12 2.12L18 20.12l8.38 8.38 2.12-2.12L20.12 18z" })
  })
});
var CloseIcon_default = CloseIcon;
var isBrowser = typeof window !== "undefined";
var candidateSelectors = [
  "input",
  "select",
  "textarea",
  "a[href]",
  "button",
  "[tabindex]",
  "audio[controls]",
  "video[controls]",
  '[contenteditable]:not([contenteditable="false"])'
];
function isHidden(node) {
  return node.offsetParent === null || getComputedStyle(node).visibility === "hidden";
}
function getCheckedRadio(nodes, form) {
  for (var i = 0; i < nodes.length; i++)
    if (nodes[i].checked && nodes[i].form === form)
      return nodes[i];
}
function isNotRadioOrTabbableRadio(node) {
  if (node.tagName !== "INPUT" || node.type !== "radio" || !node.name)
    return true;
  var radioScope = node.form || node.ownerDocument;
  var radioSet = radioScope.querySelectorAll('input[type="radio"][name="' + node.name + '"]');
  var checked = getCheckedRadio(radioSet, node.form);
  return checked === node || checked === void 0 && radioSet[0] === node;
}
function getAllTabbingElements(parentElem) {
  var currentActiveElement = document.activeElement;
  var tabbableNodes = parentElem.querySelectorAll(candidateSelectors.join(","));
  var onlyTabbable = [];
  for (var i = 0; i < tabbableNodes.length; i++) {
    var node = tabbableNodes[i];
    if (currentActiveElement === node || !node.disabled && getTabindex(node) > -1 && !isHidden(node) && isNotRadioOrTabbableRadio(node))
      onlyTabbable.push(node);
  }
  return onlyTabbable;
}
function tabTrappingKey(event, parentElem) {
  if (!event || event.key !== "Tab")
    return;
  if (!parentElem || !parentElem.contains) {
    if (process && true)
      console.warn("focus-trap-js: parent element is not defined");
    return false;
  }
  if (!parentElem.contains(event.target))
    return false;
  var allTabbingElements = getAllTabbingElements(parentElem);
  var firstFocusableElement = allTabbingElements[0];
  var lastFocusableElement = allTabbingElements[allTabbingElements.length - 1];
  if (event.shiftKey && event.target === firstFocusableElement) {
    lastFocusableElement.focus();
    event.preventDefault();
    return true;
  } else if (!event.shiftKey && event.target === lastFocusableElement) {
    firstFocusableElement.focus();
    event.preventDefault();
    return true;
  }
  return false;
}
function getTabindex(node) {
  var tabindexAttr = parseInt(node.getAttribute("tabindex"), 10);
  if (!isNaN(tabindexAttr))
    return tabindexAttr;
  if (isContentEditable(node))
    return 0;
  return node.tabIndex;
}
function isContentEditable(node) {
  return node.getAttribute("contentEditable");
}
var FocusTrap = ({ container, initialFocusRef }) => {
  const refLastFocus = (0, import_react3.useRef)(null);
  (0, import_react3.useEffect)(() => {
    const handleKeyEvent = (event) => {
      if (container == null ? void 0 : container.current)
        tabTrappingKey(event, container.current);
    };
    if (isBrowser)
      document.addEventListener("keydown", handleKeyEvent);
    if (isBrowser && (container == null ? void 0 : container.current)) {
      const savePreviousFocus = () => {
        if (candidateSelectors.findIndex((selector) => {
          var _a;
          return (_a = document.activeElement) == null ? void 0 : _a.matches(selector);
        }) !== -1)
          refLastFocus.current = document.activeElement;
      };
      if (initialFocusRef) {
        savePreviousFocus();
        requestAnimationFrame(() => {
          var _a;
          (_a = initialFocusRef.current) == null ? void 0 : _a.focus();
        });
      } else {
        const allTabbingElements = getAllTabbingElements(container.current);
        if (allTabbingElements[0]) {
          savePreviousFocus();
          allTabbingElements[0].focus();
        }
      }
    }
    return () => {
      var _a;
      if (isBrowser) {
        document.removeEventListener("keydown", handleKeyEvent);
        (_a = refLastFocus.current) == null ? void 0 : _a.focus();
      }
    };
  }, [container, initialFocusRef]);
  return null;
};
var modals = [];
var modalManager = {
  add: (newModal) => {
    modals.push(newModal);
  },
  remove: (oldModal) => {
    modals = modals.filter((modal) => modal !== oldModal);
  },
  isTopModal: (modal) => !!modals.length && modals[modals.length - 1] === modal
};
function useModalManager(ref, open) {
  (0, import_react3.useEffect)(() => {
    if (open)
      modalManager.add(ref);
    return () => {
      modalManager.remove(ref);
    };
  }, [open, ref]);
}
var useScrollLock = (refModal, open, showPortal, blockScroll, reserveScrollBarGap) => {
  const oldRef = (0, import_react3.useRef)(null);
  (0, import_react3.useEffect)(() => {
    if (open && refModal.current && blockScroll) {
      oldRef.current = refModal.current;
      disableBodyScroll(refModal.current, { reserveScrollBarGap });
    }
    return () => {
      if (oldRef.current) {
        enableBodyScroll(oldRef.current);
        oldRef.current = null;
      }
    };
  }, [
    open,
    showPortal,
    refModal,
    blockScroll,
    reserveScrollBarGap
  ]);
};
var classes = {
  root: "react-responsive-modal-root",
  overlay: "react-responsive-modal-overlay",
  overlayAnimationIn: "react-responsive-modal-overlay-in",
  overlayAnimationOut: "react-responsive-modal-overlay-out",
  modalContainer: "react-responsive-modal-container",
  modalContainerCenter: "react-responsive-modal-containerCenter",
  modal: "react-responsive-modal-modal",
  modalAnimationIn: "react-responsive-modal-modal-in",
  modalAnimationOut: "react-responsive-modal-modal-out",
  closeButton: "react-responsive-modal-closeButton"
};
var Modal = import_react3.default.forwardRef(({ open, center, blockScroll = true, closeOnEsc = true, closeOnOverlayClick = true, container, showCloseIcon = true, closeIconId, closeIcon, focusTrapped = true, initialFocusRef = void 0, animationDuration = 300, classNames, styles, role = "dialog", ariaDescribedby, ariaLabelledby, containerId, modalId, onClose, onEscKeyDown, onOverlayClick, onAnimationEnd, children, reserveScrollBarGap }, ref) => {
  const refDialog = c2(ref);
  const refModal = (0, import_react3.useRef)(null);
  const refShouldClose = (0, import_react3.useRef)(null);
  const refContainer = (0, import_react3.useRef)(null);
  if (refContainer.current === null && isBrowser)
    refContainer.current = document.createElement("div");
  const [showPortal, setShowPortal] = (0, import_react3.useState)(false);
  useModalManager(refModal, open);
  useScrollLock(refModal, open, showPortal, blockScroll, reserveScrollBarGap);
  const handleOpen = () => {
    if (refContainer.current && !container && !document.body.contains(refContainer.current))
      document.body.appendChild(refContainer.current);
    document.addEventListener("keydown", handleKeydown);
  };
  const handleClose = () => {
    if (refContainer.current && !container && document.body.contains(refContainer.current))
      document.body.removeChild(refContainer.current);
    document.removeEventListener("keydown", handleKeydown);
  };
  const handleKeydown = (event) => {
    if (event.keyCode !== 27 || !modalManager.isTopModal(refModal))
      return;
    onEscKeyDown == null ? void 0 : onEscKeyDown(event);
    if (closeOnEsc)
      onClose();
  };
  (0, import_react3.useEffect)(() => {
    return () => {
      if (showPortal)
        handleClose();
    };
  }, [showPortal]);
  (0, import_react3.useEffect)(() => {
    if (open && !showPortal) {
      setShowPortal(true);
      handleOpen();
    }
  }, [open]);
  const handleClickOverlay = (event) => {
    if (refShouldClose.current === null)
      refShouldClose.current = true;
    if (!refShouldClose.current) {
      refShouldClose.current = null;
      return;
    }
    onOverlayClick == null ? void 0 : onOverlayClick(event);
    if (closeOnOverlayClick)
      onClose();
    refShouldClose.current = null;
  };
  const handleModalEvent = () => {
    refShouldClose.current = false;
  };
  const handleAnimationEnd = () => {
    if (!open)
      setShowPortal(false);
    onAnimationEnd == null ? void 0 : onAnimationEnd();
  };
  const containerModal = container || refContainer.current;
  const overlayAnimation = open ? (classNames == null ? void 0 : classNames.overlayAnimationIn) ?? classes.overlayAnimationIn : (classNames == null ? void 0 : classNames.overlayAnimationOut) ?? classes.overlayAnimationOut;
  const modalAnimation = open ? (classNames == null ? void 0 : classNames.modalAnimationIn) ?? classes.modalAnimationIn : (classNames == null ? void 0 : classNames.modalAnimationOut) ?? classes.modalAnimationOut;
  return showPortal && containerModal ? (0, import_react_dom.createPortal)((0, import_jsx_runtime.jsxs)("div", {
    className: (0, import_classnames.default)(classes.root, classNames == null ? void 0 : classNames.root),
    style: styles == null ? void 0 : styles.root,
    "data-testid": "root",
    children: [(0, import_jsx_runtime.jsx)("div", {
      className: (0, import_classnames.default)(classes.overlay, classNames == null ? void 0 : classNames.overlay),
      "data-testid": "overlay",
      "aria-hidden": true,
      style: {
        animation: `${overlayAnimation} ${animationDuration}ms`,
        ...styles == null ? void 0 : styles.overlay
      }
    }), (0, import_jsx_runtime.jsx)("div", {
      ref: refModal,
      id: containerId,
      className: (0, import_classnames.default)(classes.modalContainer, center && classes.modalContainerCenter, classNames == null ? void 0 : classNames.modalContainer),
      style: styles == null ? void 0 : styles.modalContainer,
      "data-testid": "modal-container",
      onClick: handleClickOverlay,
      children: (0, import_jsx_runtime.jsxs)("div", {
        ref: refDialog,
        className: (0, import_classnames.default)(classes.modal, classNames == null ? void 0 : classNames.modal),
        style: {
          animation: `${modalAnimation} ${animationDuration}ms`,
          ...styles == null ? void 0 : styles.modal
        },
        onMouseDown: handleModalEvent,
        onMouseUp: handleModalEvent,
        onClick: handleModalEvent,
        onAnimationEnd: handleAnimationEnd,
        id: modalId,
        role,
        "aria-modal": "true",
        "aria-labelledby": ariaLabelledby,
        "aria-describedby": ariaDescribedby,
        "data-testid": "modal",
        tabIndex: -1,
        children: [
          focusTrapped && (0, import_jsx_runtime.jsx)(FocusTrap, {
            container: refDialog,
            initialFocusRef
          }),
          children,
          showCloseIcon && (0, import_jsx_runtime.jsx)(CloseIcon_default, {
            classes,
            classNames,
            styles,
            closeIcon,
            onClick: onClose,
            id: closeIconId
          })
        ]
      })
    })]
  }), containerModal) : null;
});
var src_default = Modal;
export {
  Modal,
  src_default as default
};
/*! Bundled license information:

classnames/index.js:
  (*!
  	Copyright (c) 2018 Jed Watson.
  	Licensed under the MIT License (MIT), see
  	http://jedwatson.github.io/classnames
  *)
*/
//# sourceMappingURL=react-responsive-modal.js.map
